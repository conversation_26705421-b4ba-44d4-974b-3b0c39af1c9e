'use client';

import React from 'react';
import { EmployerNavbar } from './EmployerNavbar';

interface EmployerLayoutProps {
  children: React.ReactNode;
  newApplications?: number;
  activeJobs?: number;
}

export function EmployerLayout({
  children,
  newApplications = 0,
  activeJobs = 0
}: EmployerLayoutProps) {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <EmployerNavbar
        newApplications={newApplications}
        activeJobs={activeJobs}
      />

      {/* Main content */}
      <div className="lg:ml-64 relative z-10">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
