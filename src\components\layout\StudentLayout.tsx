'use client';

import React from 'react';
import { StudentNavbar } from './StudentNavbar';

interface StudentLayoutProps {
  children: React.ReactNode;
  coursesEnrolled?: number;
  pendingApplications?: number;
  upcomingSessions?: number;
}

export function StudentLayout({
  children,
  coursesEnrolled = 0,
  pendingApplications = 0,
  upcomingSessions = 0
}: StudentLayoutProps) {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <StudentNavbar
        coursesEnrolled={coursesEnrolled}
        pendingApplications={pendingApplications}
        upcomingSessions={upcomingSessions}
      />

      {/* Main content */}
      <div className="lg:ml-64 relative z-10">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
