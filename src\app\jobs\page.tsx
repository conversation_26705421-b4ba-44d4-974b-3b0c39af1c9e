'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import {
  Briefcase,
  MapPin,
  Clock,
  DollarSign,
  Building,
  Search,
  Filter,
  Calendar,
  ArrowRight,
  Star,
  Users
} from 'lucide-react';

// Mock data for jobs
const mockJobs = [
  {
    id: 1,
    title: 'Satellite Systems Engineer',
    company: 'Kenya Space Agency',
    location: 'Nairobi, Kenya',
    type: 'Full-time',
    remote: false,
    salary: { min: 80000, max: 120000, currency: 'USD' },
    postedDate: '2 days ago',
    description: 'Design and develop satellite systems for Earth observation missions.',
    requirements: ['Bachelor\'s in Aerospace Engineering', '3+ years experience', 'MATLAB/Simulink'],
    logo: '/api/placeholder/60/60',
    featured: true,
    applicants: 23,
  },
  {
    id: 2,
    title: 'AI Research Scientist - Space Applications',
    company: 'Ghana Space Science Institute',
    location: 'Accra, Ghana',
    type: 'Full-time',
    remote: true,
    salary: { min: 90000, max: 140000, currency: 'USD' },
    postedDate: '1 week ago',
    description: 'Develop AI algorithms for satellite data analysis and space mission planning.',
    requirements: ['PhD in AI/ML', 'Python/TensorFlow', 'Space domain knowledge'],
    logo: '/api/placeholder/60/60',
    featured: false,
    applicants: 45,
  },
  {
    id: 3,
    title: 'Cybersecurity Specialist - Space Systems',
    company: 'Egyptian Space Agency',
    location: 'Cairo, Egypt',
    type: 'Contract',
    remote: false,
    salary: { min: 70000, max: 100000, currency: 'USD' },
    postedDate: '3 days ago',
    description: 'Secure space infrastructure and satellite communication systems.',
    requirements: ['Cybersecurity certification', 'Network security', '5+ years experience'],
    logo: '/api/placeholder/60/60',
    featured: true,
    applicants: 18,
  },
  {
    id: 4,
    title: 'Space Data Analyst Intern',
    company: 'South African Space Agency',
    location: 'Cape Town, South Africa',
    type: 'Internship',
    remote: true,
    salary: { min: 2000, max: 3000, currency: 'USD' },
    postedDate: '5 days ago',
    description: 'Analyze satellite imagery and space mission data for research projects.',
    requirements: ['Student in relevant field', 'Python/R', 'Data analysis skills'],
    logo: '/api/placeholder/60/60',
    featured: false,
    applicants: 67,
  },
];

const jobTypes = ['All Types', 'Full-time', 'Part-time', 'Contract', 'Internship', 'Apprenticeship'];
const locations = ['All Locations', 'Remote', 'Kenya', 'Ghana', 'Egypt', 'South Africa', 'Nigeria'];

export default function JobsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All Types');
  const [selectedLocation, setSelectedLocation] = useState('All Locations');
  const [showRemoteOnly, setShowRemoteOnly] = useState(false);

  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <Header />

      {/* Hero Section */}
      <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
        {/* Cosmic Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
          <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
        </div>

        <div className="relative section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <Briefcase className="h-16 w-16 galaxy-text-blue" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
              Space Technology Jobs
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Discover exciting career opportunities in space technology across Africa.
              From internships to senior positions, find your perfect role.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-gray-300">
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Briefcase className="h-5 w-5 galaxy-text-blue" />
                <span>500+ Active Jobs</span>
              </div>
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Building className="h-5 w-5 galaxy-text-purple" />
                <span>50+ Companies</span>
              </div>
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <MapPin className="h-5 w-5 galaxy-text-pink" />
                <span>25 Countries</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Job Type Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {jobTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Location Filter */}
            <div>
              <select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {locations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>

            {/* Remote Only Filter */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showRemoteOnly}
                onChange={(e) => setShowRemoteOnly(e.target.checked)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <span className="text-gray-700">Remote only</span>
            </label>
          </div>
        </div>
      </section>

      {/* Jobs List */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            {mockJobs.map((job, index) => (
              <motion.div
                key={job.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${
                  job.featured ? 'border-l-4 border-green-500' : ''
                }`}
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  {/* Job Info */}
                  <div className="flex-1">
                    <div className="flex items-start space-x-4">
                      {/* Company Logo */}
                      <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
                        {job.company.split(' ').map(word => word[0]).join('')}
                      </div>

                      {/* Job Details */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-xl font-semibold text-gray-900">
                            {job.title}
                          </h3>
                          {job.featured && (
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                              Featured
                            </span>
                          )}
                          {job.remote && (
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                              Remote
                            </span>
                          )}
                        </div>

                        <div className="flex items-center space-x-4 text-gray-600 mb-3">
                          <div className="flex items-center space-x-1">
                            <Building className="h-4 w-4" />
                            <span>{job.company}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-4 w-4" />
                            <span>{job.location}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{job.type}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{job.postedDate}</span>
                          </div>
                        </div>

                        <p className="text-gray-700 mb-4 line-clamp-2">
                          {job.description}
                        </p>

                        {/* Requirements */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {job.requirements.slice(0, 3).map((req, idx) => (
                            <span
                              key={idx}
                              className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                            >
                              {req}
                            </span>
                          ))}
                        </div>

                        {/* Stats */}
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>{job.applicants} applicants</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <DollarSign className="h-4 w-4" />
                            <span>
                              ${job.salary.min.toLocaleString()} - ${job.salary.max.toLocaleString()} {job.salary.currency}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Apply Button */}
                  <div className="mt-4 lg:mt-0 lg:ml-6">
                    <button className="w-full lg:w-auto bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center space-x-2">
                      <span>Apply Now</span>
                      <ArrowRight className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <button className="bg-gray-200 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-300 transition-colors duration-200">
              Load More Jobs
            </button>
          </div>
        </div>
      </section>

      {/* Job Alerts Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Never Miss an Opportunity
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Get notified about new job opportunities that match your skills and interests.
            </p>

            <div className="max-w-md mx-auto">
              <div className="flex">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <button className="bg-green-600 text-white px-6 py-3 rounded-r-lg hover:bg-green-700 transition-colors duration-200">
                  Subscribe
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Get weekly job alerts via email and SMS
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* For Employers Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold mb-4">
              Hiring Space Tech Talent?
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
              Post your jobs and connect with qualified candidates from across Africa's space technology ecosystem.
            </p>
            <button className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-2 mx-auto">
              <span>Post a Job</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
