'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Video,
  Search,
  Filter,
  ArrowRight,
  Star,
  Globe,
  Ticket
} from 'lucide-react';

// Mock data for events
const mockEvents = [
  {
    id: 1,
    title: 'African Space Technology Summit 2024',
    description: 'Join industry leaders, researchers, and innovators for the premier space technology conference in Africa.',
    date: '2024-03-15',
    time: '09:00 AM',
    duration: '3 days',
    location: 'Nairobi, Kenya',
    type: 'Conference',
    format: 'In-person',
    price: 250,
    isFree: false,
    attendees: 500,
    maxAttendees: 800,
    organizer: 'Kenya Space Agency',
    image: '/api/placeholder/400/250',
    featured: true,
    tags: ['Networking', 'Industry Leaders', 'Innovation'],
  },
  {
    id: 2,
    title: 'AI in Space Exploration Webinar',
    description: 'Explore how artificial intelligence is revolutionizing space missions and satellite data analysis.',
    date: '2024-02-28',
    time: '02:00 PM',
    duration: '2 hours',
    location: 'Online',
    type: 'Webinar',
    format: 'Virtual',
    price: 0,
    isFree: true,
    attendees: 1200,
    maxAttendees: 2000,
    organizer: 'Ghana Space Science Institute',
    image: '/api/placeholder/400/250',
    featured: false,
    tags: ['AI', 'Machine Learning', 'Satellites'],
  },
  {
    id: 3,
    title: 'Space Tech Career Fair',
    description: 'Connect with leading space technology companies and explore career opportunities across Africa.',
    date: '2024-03-08',
    time: '10:00 AM',
    duration: '1 day',
    location: 'Cape Town, South Africa',
    type: 'Career Fair',
    format: 'Hybrid',
    price: 0,
    isFree: true,
    attendees: 300,
    maxAttendees: 500,
    organizer: 'South African Space Agency',
    image: '/api/placeholder/400/250',
    featured: true,
    tags: ['Jobs', 'Networking', 'Career Development'],
  },
  {
    id: 4,
    title: 'Satellite Engineering Workshop',
    description: 'Hands-on workshop covering satellite design, orbital mechanics, and mission planning.',
    date: '2024-03-22',
    time: '09:00 AM',
    duration: '2 days',
    location: 'Cairo, Egypt',
    type: 'Workshop',
    format: 'In-person',
    price: 150,
    isFree: false,
    attendees: 45,
    maxAttendees: 60,
    organizer: 'Egyptian Space Agency',
    image: '/api/placeholder/400/250',
    featured: false,
    tags: ['Hands-on', 'Technical', 'Engineering'],
  },
];

const eventTypes = ['All Types', 'Conference', 'Webinar', 'Workshop', 'Career Fair', 'Networking'];
const eventFormats = ['All Formats', 'In-person', 'Virtual', 'Hybrid'];

export default function EventsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All Types');
  const [selectedFormat, setSelectedFormat] = useState('All Formats');
  const [showFreeOnly, setShowFreeOnly] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <Header />

      {/* Hero Section */}
      <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
        {/* Cosmic Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
          <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
        </div>

        <div className="relative section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <Calendar className="h-16 w-16 galaxy-text-blue" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
              Space Technology Events
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Join webinars, workshops, conferences, and networking events to stay connected
              with Africa's space technology community.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-gray-300">
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Calendar className="h-5 w-5 galaxy-text-blue" />
                <span>50+ Events Monthly</span>
              </div>
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Users className="h-5 w-5 galaxy-text-purple" />
                <span>10,000+ Attendees</span>
              </div>
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Globe className="h-5 w-5 galaxy-text-pink" />
                <span>25 Countries</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="relative py-8 z-10">
        <div className="section-container">
          <div className="muted-glassmorphic rounded-2xl p-6 glow-border">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 galaxy-text-blue h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search events..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 glow-border"
                />
              </div>

              {/* Event Type Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 galaxy-text-purple" />
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="px-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white glow-border"
                >
                  {eventTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              {/* Format Filter */}
              <div>
                <select
                  value={selectedFormat}
                  onChange={(e) => setSelectedFormat(e.target.value)}
                  className="px-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white glow-border"
                >
                  {eventFormats.map(format => (
                    <option key={format} value={format}>{format}</option>
                  ))}
                </select>
              </div>

              {/* Free Only Filter */}
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={showFreeOnly}
                  onChange={(e) => setShowFreeOnly(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-gray-300">Free events only</span>
              </label>
            </div>
          </div>
        </div>
      </section>

      {/* Events Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockEvents.map((event, index) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              >
                {/* Event Image */}
                <div className="relative h-48 bg-gradient-to-br from-orange-500 to-red-600">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Calendar className="h-16 w-16 text-white opacity-80" />
                  </div>
                  {event.featured && (
                    <div className="absolute top-3 left-3 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      Featured
                    </div>
                  )}
                  {event.isFree && (
                    <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      Free
                    </div>
                  )}
                  <div className="absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                    {event.format}
                  </div>
                </div>

                {/* Event Content */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full">
                      {event.type}
                    </span>
                    <span className="text-xs text-gray-500">
                      {event.organizer}
                    </span>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {event.title}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {event.description}
                  </p>

                  {/* Event Details */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{formatDate(event.date)}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>{event.time} • {event.duration}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      {event.format === 'Virtual' ? (
                        <Video className="h-4 w-4 mr-2" />
                      ) : (
                        <MapPin className="h-4 w-4 mr-2" />
                      )}
                      <span>{event.location}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {event.tags.slice(0, 2).map((tag, idx) => (
                      <span
                        key={idx}
                        className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Attendees and Price */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{event.attendees}/{event.maxAttendees}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Ticket className="h-4 w-4" />
                      <span>{event.isFree ? 'Free' : `$${event.price}`}</span>
                    </div>
                  </div>

                  {/* Register Button */}
                  <button className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors duration-200 flex items-center justify-center space-x-2">
                    <span>Register Now</span>
                    <ArrowRight className="h-4 w-4" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Upcoming Events Calendar */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              This Month's Highlights
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Don't miss these upcoming events that will shape the future of space technology in Africa.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { date: '15', month: 'MAR', title: 'Space Summit', type: 'Conference' },
              { date: '22', month: 'MAR', title: 'Satellite Workshop', type: 'Workshop' },
              { date: '28', month: 'MAR', title: 'AI Webinar', type: 'Webinar' },
              { date: '05', month: 'APR', title: 'Career Fair', type: 'Networking' },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gradient-to-br from-orange-500 to-red-600 text-white rounded-xl p-6 text-center"
              >
                <div className="text-3xl font-bold mb-1">{item.date}</div>
                <div className="text-orange-100 text-sm mb-3">{item.month}</div>
                <div className="font-semibold mb-1">{item.title}</div>
                <div className="text-orange-100 text-sm">{item.type}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Host Event CTA */}
      <section className="py-16 bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold mb-4">
              Want to Host an Event?
            </h2>
            <p className="text-xl text-orange-100 mb-8 max-w-2xl mx-auto">
              Share your expertise with the space technology community. Host webinars, workshops, or conferences.
            </p>
            <button className="bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-2 mx-auto">
              <span>Host an Event</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
