'use client';

import React from 'react';
import { AdminNavbar } from './AdminNavbar';

interface AdminLayoutProps {
  children: React.ReactNode;
  pendingApprovals?: number;
  systemAlerts?: number;
}

export function AdminLayout({ children, pendingApprovals, systemAlerts }: AdminLayoutProps) {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <AdminNavbar pendingApprovals={pendingApprovals} systemAlerts={systemAlerts} />

      {/* Main content */}
      <div className="lg:ml-64 relative z-10">
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
