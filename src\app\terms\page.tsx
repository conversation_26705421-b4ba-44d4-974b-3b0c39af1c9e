'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Shield, FileText, Calendar } from 'lucide-react';

export default function TermsPage() {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <Header />

      {/* Hero Section */}
      <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
        {/* Cosmic Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
          <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
        </div>

        <div className="relative section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <Shield className="h-16 w-16 galaxy-text-blue" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
              Terms of Service
            </h1>
            <p className="text-xl galaxy-text-blue mb-6">
              Last updated: January 1, 2024
            </p>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Please read these Terms of Service carefully before using the Nova platform.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-xl shadow-lg p-8"
          >
            <div className="prose prose-lg max-w-none">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-700 mb-6">
                By accessing and using the Nova platform ("Service"), you accept and agree to be bound by the terms
                and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">2. Description of Service</h2>
              <p className="text-gray-700 mb-6">
                Nova is an online learning platform that provides space technology education, mentorship programs,
                job opportunities, and community features. The Service includes access to courses, mentorship sessions,
                job listings, events, and community forums.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">3. User Accounts</h2>
              <p className="text-gray-700 mb-4">
                To access certain features of the Service, you must register for an account. You agree to:
              </p>
              <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                <li>Provide accurate, current, and complete information during registration</li>
                <li>Maintain and promptly update your account information</li>
                <li>Maintain the security of your password and account</li>
                <li>Accept responsibility for all activities under your account</li>
                <li>Notify us immediately of any unauthorized use of your account</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">4. User Conduct</h2>
              <p className="text-gray-700 mb-4">
                You agree not to use the Service to:
              </p>
              <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                <li>Upload, post, or transmit any content that is unlawful, harmful, threatening, abusive, or offensive</li>
                <li>Impersonate any person or entity or misrepresent your affiliation with any person or entity</li>
                <li>Interfere with or disrupt the Service or servers connected to the Service</li>
                <li>Attempt to gain unauthorized access to any portion of the Service</li>
                <li>Use the Service for any commercial purpose without our express written consent</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Intellectual Property</h2>
              <p className="text-gray-700 mb-6">
                The Service and its original content, features, and functionality are and will remain the exclusive
                property of Nova and its licensors. The Service is protected by copyright, trademark, and other laws.
                You may not reproduce, distribute, modify, or create derivative works of any part of the Service
                without our express written permission.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Course Content and Certificates</h2>
              <p className="text-gray-700 mb-6">
                Course content is provided for educational purposes only. Certificates issued upon course completion
                are digital credentials that verify your participation and achievement in our courses. While we strive
                to provide high-quality education, we do not guarantee specific career outcomes or job placement.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">7. Mentorship Services</h2>
              <p className="text-gray-700 mb-6">
                Mentorship sessions are facilitated through our platform but the actual mentoring relationship is
                between you and the mentor. We are not responsible for the quality, accuracy, or outcomes of mentorship
                sessions. All mentors are independent contractors and their views do not necessarily represent Nova's views.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">8. Payment and Refunds</h2>
              <p className="text-gray-700 mb-6">
                Some features of the Service may require payment. All fees are non-refundable unless otherwise stated.
                We reserve the right to change our pricing at any time. If you are dissatisfied with a paid service,
                please contact our support team within 30 days of purchase.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">9. Privacy</h2>
              <p className="text-gray-700 mb-6">
                Your privacy is important to us. Please review our Privacy Policy, which also governs your use of
                the Service, to understand our practices regarding the collection and use of your personal information.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">10. Termination</h2>
              <p className="text-gray-700 mb-6">
                We may terminate or suspend your account and access to the Service immediately, without prior notice
                or liability, for any reason whatsoever, including without limitation if you breach the Terms.
                Upon termination, your right to use the Service will cease immediately.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">11. Disclaimer of Warranties</h2>
              <p className="text-gray-700 mb-6">
                The Service is provided on an "AS IS" and "AS AVAILABLE" basis. Nova expressly disclaims all warranties
                of any kind, whether express or implied, including but not limited to the implied warranties of
                merchantability, fitness for a particular purpose, and non-infringement.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">12. Limitation of Liability</h2>
              <p className="text-gray-700 mb-6">
                In no event shall Nova, its directors, employees, partners, agents, suppliers, or affiliates be liable
                for any indirect, incidental, special, consequential, or punitive damages, including without limitation,
                loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the Service.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">13. Governing Law</h2>
              <p className="text-gray-700 mb-6">
                These Terms shall be interpreted and governed by the laws of Kenya, without regard to its conflict
                of law provisions. Any disputes arising from these Terms or your use of the Service shall be subject
                to the exclusive jurisdiction of the courts of Kenya.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">14. Changes to Terms</h2>
              <p className="text-gray-700 mb-6">
                We reserve the right to modify or replace these Terms at any time. If a revision is material,
                we will try to provide at least 30 days notice prior to any new terms taking effect.
                Your continued use of the Service after any such changes constitutes your acceptance of the new Terms.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">15. Contact Information</h2>
              <p className="text-gray-700 mb-6">
                If you have any questions about these Terms of Service, please contact us at:
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-700">
                  <strong>Email:</strong> <EMAIL><br />
                  <strong>Address:</strong> Nova Innovation Hub, University Way, Nairobi, Kenya
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
